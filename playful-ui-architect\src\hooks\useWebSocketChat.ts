import { useState, useCallback, useRef } from 'react';
import useWebSocket, { WebSocketMessage } from './useWebSocket';
import {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  SendMessageResponse,
} from '@/store/api/chatApiSlice';

interface WebSocketChatOptions {
  url: string;
  token: string;
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  enabled?: boolean;
}

interface PendingRequest {
  resolve: (value: unknown) => void;
  reject: (error: unknown) => void;
  timeout: NodeJS.Timeout;
}

interface WebSocketResponse {
  data?: {
    conversations?: Conversation[];
    conversation?: Conversation;
    messages?: Message[];
    [key: string]: unknown;
  };
  success?: boolean;
  error?: string;
}

export const useWebSocketChat = ({
  url,
  token,
  userId,
  userType,
  userName,
  enabled = true,
}: WebSocketChatOptions) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Record<string, Message[]>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [lastUpdate, setLastUpdate] = useState<number>(Date.now());
  const [messageCounter, setMessageCounter] = useState<number>(0);

  const pendingRequests = useRef<Map<string, PendingRequest>>(new Map());

  const isDuplicateMessage = useCallback(
    (existingMessages: Message[], messageId: string) => {
      return existingMessages.some(msg => msg.id === messageId);
    },
    []
  );

  const filterOptimisticMessages = useCallback(
    (existingMessages: Message[], newMessage: Message) => {
      return existingMessages.filter(msg => {
        const isOptimistic =
          msg.id.startsWith('temp-') && msg.status === 'sending';
        const matchesContent =
          msg.content === newMessage.content &&
          msg.senderId === newMessage.senderId;
        const isRecent =
          Math.abs(
            new Date(msg.timestamp).getTime() -
              new Date(newMessage.timestamp).getTime()
          ) < 10000;

        return !(isOptimistic && matchesContent && isRecent);
      });
    },
    []
  );

  const updateMessagesToRead = useCallback(
    (conversationId: string, messageId?: string, readByUserId?: string) => {
      setMessages(prev => {
        const conversationMessages = prev[conversationId] || [];
        const updatedMessages = conversationMessages.map(msg => {
          if (messageId && msg.id === messageId) {
            if (
              msg.senderId === userId &&
              readByUserId !== userId &&
              msg.status !== 'read'
            ) {
              return { ...msg, status: 'read' as const };
            }
          } else if (
            !messageId &&
            msg.senderId === userId &&
            readByUserId !== userId &&
            msg.status !== 'read'
          ) {
            return { ...msg, status: 'read' as const };
          }
          return msg;
        });

        return {
          ...prev,
          [conversationId]: updatedMessages,
        };
      });

      setLastUpdate(Date.now());
    },
    [userId]
  );

  const setLoadingAndClearError = useCallback(
    (key: string, isLoading: boolean) => {
      setLoading(prev => ({ ...prev, [key]: isLoading }));
      if (isLoading) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[key];
          return newErrors;
        });
      }
    },
    []
  );

  const handleOperationError = useCallback(
    (key: string, error: unknown, defaultMessage: string) => {
      const errorMessage =
        error instanceof Error ? error.message : defaultMessage;
      setErrors(prev => ({ ...prev, [key]: errorMessage }));
      setLoading(prev => ({ ...prev, [key]: false }));
    },
    []
  );

  const handleWebSocketMessage = useCallback(
    (message: WebSocketMessage) => {
      if (
        (message.type === 'TEXT_MESSAGE' || message.type === 'new_message') &&
        message.conversationId
      ) {
        const messageId =
          (message as WebSocketMessage & { messageId?: string }).messageId ||
          String(Date.now());

        const newMessage: Message = {
          id: messageId,
          conversationId: message.conversationId,
          senderId: message.senderId || '',
          senderType: message.senderType || 'patient',
          senderName: message.senderName || 'Unknown',
          content: message.content || '',
          type: 'text',
          status: 'sent',
          timestamp: message.timestamp || new Date().toISOString(),
        };

        setMessages(prev => {
          const existingMessages = prev[message.conversationId!] || [];

          if (isDuplicateMessage(existingMessages, messageId)) {
            return prev;
          }

          const filteredMessages = filterOptimisticMessages(
            existingMessages,
            newMessage
          );

          if (isDuplicateMessage(filteredMessages, messageId)) {
            return prev;
          }

          const updatedMessages = [...filteredMessages, newMessage].sort(
            (a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );

          return {
            ...prev,
            [message.conversationId!]: [...updatedMessages],
          };
        });

        setLastUpdate(Date.now());
        setMessageCounter(prev => prev + 1);

        setConversations(prev =>
          prev.map(conv =>
            conv.id === message.conversationId
              ? {
                  ...conv,
                  lastMessage: message.content,
                  lastMessageTime: message.timestamp,
                }
              : conv
          )
        );
        return;
      }

      if (message.type === 'MESSAGE_SENT' && message.conversationId) {
        const messageId = (message as WebSocketMessage & { messageId?: string })
          .messageId;
        const timestamp = message.timestamp;

        if (messageId && timestamp) {
          setMessages(prev => {
            const conversationMessages = prev[message.conversationId] || [];

            // Find the sending message that matches by timestamp and sender
            // Look for messages sent within 5 seconds of the timestamp
            const timestampMs = new Date(timestamp).getTime();
            const tolerance = 5000; // 5 seconds

            const updatedMessages = conversationMessages.map(msg => {
              if (msg.status === 'sending' && msg.senderId === userId) {
                const msgTimestampMs = new Date(msg.timestamp).getTime();
                const timeDiff = Math.abs(timestampMs - msgTimestampMs);

                if (timeDiff <= tolerance) {
                  return { ...msg, id: messageId, status: 'sent' as const };
                }
              }
              return msg;
            });

            return {
              ...prev,
              [message.conversationId]: updatedMessages,
            };
          });

          setLastUpdate(Date.now());
        }
        return;
      }

      if (message.type === 'MESSAGE_ERROR' && message.conversationId) {
        setErrors(prev => ({
          ...prev,
          [`send-message-${message.conversationId}`]:
            message.error || 'Failed to send message',
        }));
        return;
      }

      if (message.type === 'READ_RECEIPT' && message.conversationId) {
        const messageId = (message as WebSocketMessage & { messageId?: string })
          .messageId;
        const readBy = (message as WebSocketMessage & { readBy?: string })
          .readBy;
        updateMessagesToRead(message.conversationId, messageId, readBy);
        return;
      }

      if (message.type === 'CONVERSATION_READ' && message.conversationId) {
        const readBy = (message as WebSocketMessage & { readBy?: string })
          .readBy;
        updateMessagesToRead(message.conversationId, undefined, readBy);
        return;
      }

      if (message.type === 'MESSAGES_MARKED_READ' && message.conversationId) {
        updateMessagesToRead(
          message.conversationId,
          undefined,
          message.senderId
        );
        return;
      }

      if (message.type === 'MESSAGE_STATUS_UPDATE' && message.conversationId) {
        const { messageId, status } = message as WebSocketMessage & {
          messageId: string;
          status: 'sent' | 'delivered' | 'read';
        };

        if (messageId && status) {
          setMessages(prev => {
            const conversationMessages = prev[message.conversationId] || [];
            const updatedMessages = conversationMessages.map(msg =>
              msg.id === messageId ? { ...msg, status } : msg
            );

            return {
              ...prev,
              [message.conversationId]: updatedMessages,
            };
          });

          setLastUpdate(Date.now());
        }
        return;
      }

      if (message.requestId) {
        const pendingRequest = pendingRequests.current.get(message.requestId);
        if (pendingRequest) {
          clearTimeout(pendingRequest.timeout);
          pendingRequests.current.delete(message.requestId);

          if (message.success) {
            pendingRequest.resolve(message);
          } else {
            pendingRequest.reject(new Error(message.error || 'Request failed'));
          }
        } else {
          const shouldHavePendingRequest = [
            'CONVERSATIONS_RESPONSE',
            'CONVERSATION_RESPONSE',
            'CONVERSATION_CREATED',
            'MESSAGES_RESPONSE',
            'MESSAGE_SENT',
            'SEARCH_RESPONSE',
            'CONVERSATION_STATUS_UPDATED',
            'UNREAD_COUNT_RESPONSE',
            'NURSE_INFO_RESPONSE',
          ].includes(message.type);

          if (shouldHavePendingRequest) {
            console.warn(
              'No pending request found for requestId:',
              message.requestId,
              'message type:',
              message.type
            );
          }
        }
      }
    },
    [filterOptimisticMessages, isDuplicateMessage, updateMessagesToRead, userId]
  );

  const webSocket = useWebSocket({
    url,
    token,
    userId,
    userType,
    userName,
    enabled,
    onMessage: handleWebSocketMessage,
  });

  const { sendMessage: wsSendMessage, status: wsStatus } = webSocket;

  const waitForConnection = useCallback(
    (maxWaitMs = 5000): Promise<void> => {
      return new Promise((resolve, reject) => {
        if (wsStatus.connected) {
          resolve();
          return;
        }

        const startTime = Date.now();
        const checkConnection = () => {
          if (wsStatus.connected) {
            resolve();
          } else if (Date.now() - startTime > maxWaitMs) {
            reject(new Error('WebSocket connection timeout'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };

        checkConnection();
      });
    },
    [wsStatus.connected]
  );

  const makeRequest = useCallback(
    async (
      requestMessage: WebSocketMessage,
      timeoutMs = 10000
    ): Promise<WebSocketResponse> => {
      try {
        await waitForConnection();
      } catch (error) {
        console.error(
          'WebSocket connection failed for request:',
          requestMessage.type,
          error
        );
        throw new Error('WebSocket not connected');
      }

      return new Promise((resolve, reject) => {
        const requestId = Math.random().toString(36).substring(2, 15);
        const messageWithId = { ...requestMessage, requestId };

        const timeout = setTimeout(() => {
          console.error(
            'Request timeout for:',
            requestMessage.type,
            'requestId:',
            requestId
          );
          pendingRequests.current.delete(requestId);
          reject(new Error('Request timeout'));
        }, timeoutMs);

        pendingRequests.current.set(requestId, { resolve, reject, timeout });

        const success = wsSendMessage(messageWithId);

        if (!success) {
          clearTimeout(timeout);
          pendingRequests.current.delete(requestId);
          reject(new Error('Failed to send WebSocket message'));
        }
      });
    },
    [wsSendMessage, waitForConnection]
  );

  const getConversations = useCallback(
    async (params?: { page?: number; limit?: number; status?: string }) => {
      const key = 'conversations';
      setLoadingAndClearError(key, true);

      try {
        const response = await makeRequest({
          type: 'GET_CONVERSATIONS',
          page: params?.page || 1,
          limit: params?.limit || 20,
          status: params?.status as 'active' | 'inactive' | 'archived',
          senderId: userId,
          senderType: userType,
          timestamp: new Date().toISOString(),
        });

        const conversationsData = response.data?.conversations || [];
        setConversations(conversationsData);
        setLoading(prev => ({ ...prev, [key]: false }));

        return {
          success: true,
          conversations: conversationsData,
          data: response.data,
        } as ConversationsResponse;
      } catch (error) {
        handleOperationError(key, error, 'Failed to fetch conversations');
        throw error;
      }
    },
    [
      makeRequest,
      userId,
      userType,
      setLoadingAndClearError,
      handleOperationError,
    ]
  );

  const getConversation = useCallback(
    async (conversationId: string) => {
      const key = `conversation-${conversationId}`;
      setLoadingAndClearError(key, true);

      try {
        const response = await makeRequest({
          type: 'GET_CONVERSATION',
          conversationId,
          senderId: userId,
          senderType: userType,
          timestamp: new Date().toISOString(),
        });

        setLoading(prev => ({ ...prev, [key]: false }));
        return {
          success: true,
          conversation: response.data?.conversation,
          data: response.data,
        } as ConversationResponse;
      } catch (error) {
        handleOperationError(key, error, 'Failed to fetch conversation');
        throw error;
      }
    },
    [
      makeRequest,
      userId,
      userType,
      setLoadingAndClearError,
      handleOperationError,
    ]
  );

  const createConversation = useCallback(
    async (data: {
      patientId: string;
      patientName: string;
      initialMessage?: string;
    }) => {
      const key = 'create-conversation';
      setLoadingAndClearError(key, true);

      try {
        const requestMessage: WebSocketMessage = {
          type: 'CREATE_CONVERSATION',
          patientId: data.patientId,
          patientName: data.patientName,
          initialMessage: data.initialMessage,
          senderId: userId,
          senderType: userType,
          senderName: userName,
          timestamp: new Date().toISOString(),
        };

        const response = await makeRequest(requestMessage);

        const conversation = response.data?.conversation;
        if (conversation) {
          setConversations(prev => {
            const exists = prev.find(c => c.id === conversation.id);
            return exists ? prev : [...prev, conversation];
          });
        }

        setLoading(prev => ({ ...prev, [key]: false }));
        return {
          success: true,
          conversation,
          data: response.data,
        } as ConversationResponse;
      } catch (error) {
        handleOperationError(key, error, 'Failed to create conversation');
        throw error;
      }
    },
    [
      makeRequest,
      userId,
      userType,
      userName,
      setLoadingAndClearError,
      handleOperationError,
    ]
  );

  const getMessages = useCallback(
    async (params: {
      conversationId: string;
      page?: number;
      limit?: number;
    }) => {
      const key = `messages-${params.conversationId}`;
      setLoadingAndClearError(key, true);

      try {
        const response = await makeRequest({
          type: 'GET_MESSAGES',
          conversationId: params.conversationId,
          page: params.page || 1,
          limit: params.limit || 50,
          senderId: userId,
          senderType: userType,
          timestamp: new Date().toISOString(),
        });

        const messagesData = response.data?.messages || [];

        setMessages(prev => {
          const existingMessages = prev[params.conversationId] || [];

          if (existingMessages.length > 0) {
            const existingMessagesMap = new Map(
              existingMessages.map(msg => [msg.id, msg])
            );

            const newMessages = messagesData.filter(
              msg => !existingMessagesMap.has(msg.id)
            );

            const allMessages = [...existingMessages, ...newMessages].sort(
              (a, b) =>
                new Date(a.timestamp).getTime() -
                new Date(b.timestamp).getTime()
            );

            return {
              ...prev,
              [params.conversationId]: allMessages,
            };
          } else {
            return {
              ...prev,
              [params.conversationId]: messagesData,
            };
          }
        });

        setLoading(prev => ({ ...prev, [key]: false }));
        return {
          success: true,
          messages: messagesData,
          data: response.data,
        } as MessagesResponse;
      } catch (error) {
        handleOperationError(key, error, 'Failed to fetch messages');
        throw error;
      }
    },
    [
      makeRequest,
      userId,
      userType,
      setLoadingAndClearError,
      handleOperationError,
    ]
  );

  const sendMessage = useCallback(
    async (data: {
      conversationId: string;
      content: string;
      type?: string;
    }) => {
      const key = `send-message-${data.conversationId}`;
      setLoadingAndClearError(key, true);

      try {
        const success = webSocket.sendTextMessage(
          data.conversationId,
          data.content
        );

        if (!success) {
          throw new Error('Failed to send message via WebSocket');
        }

        const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        const optimisticMessage: Message = {
          id: tempId,
          conversationId: data.conversationId,
          senderId: userId,
          senderType: userType,
          senderName: userName,
          content: data.content,
          type: 'text',
          status: 'sending',
          timestamp: new Date().toISOString(),
        };

        setMessages(prev => {
          const existingMessages = prev[data.conversationId] || [];

          const isDuplicate = existingMessages.some(
            msg =>
              msg.content === data.content &&
              msg.senderId === userId &&
              Math.abs(
                new Date(msg.timestamp).getTime() -
                  new Date(optimisticMessage.timestamp).getTime()
              ) < 1000
          );

          if (isDuplicate) {
            return prev;
          }

          return {
            ...prev,
            [data.conversationId]: [...existingMessages, optimisticMessage],
          };
        });

        setLoading(prev => ({ ...prev, [key]: false }));
        return {
          success: true,
          messageData: optimisticMessage,
        } as SendMessageResponse;
      } catch (error) {
        handleOperationError(key, error, 'Failed to send message');
        throw error;
      }
    },
    [
      webSocket,
      userId,
      userType,
      userName,
      setLoadingAndClearError,
      handleOperationError,
    ]
  );

  return {
    status: wsStatus,

    conversations,
    messages,
    loading,
    errors,
    lastUpdate,
    messageCounter,

    connect: webSocket.connect,
    disconnect: webSocket.disconnect,
    joinConversation: webSocket.joinConversation,
    leaveConversation: webSocket.leaveConversation,
    sendTypingIndicator: webSocket.sendTypingIndicator,
    sendReadReceipt: webSocket.sendReadReceipt,
    markMessagesAsRead: webSocket.markMessagesAsRead,
    typingUsers: webSocket.typingUsers,

    getConversations,
    getConversation,
    createConversation,
    getMessages,
    sendMessage,

    clearError: (key: string) => setErrors(prev => ({ ...prev, [key]: '' })),
    clearMessages: (conversationId: string) =>
      setMessages(prev => ({ ...prev, [conversationId]: [] })),
  };
};

export default useWebSocketChat;
